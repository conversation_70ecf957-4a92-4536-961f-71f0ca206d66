#!/usr/bin/env python3
"""
调试版本的main.py
"""
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import logging
import sys
import uvicorn

# 强制输出函数
def force_log(msg):
    print(f"[FORCE LOG] {msg}", flush=True)
    with open("debug.log", "a", encoding="utf-8") as f:
        f.write(f"[DEBUG] {msg}\n")
        f.flush()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('app_debug.log', encoding='utf-8')
    ],
    force=True
)

logger = logging.getLogger(__name__)
force_log("Debug main.py starting...")

app = FastAPI(title="Debug Coze Workflow API")

class WorkflowRequest(BaseModel):
    workflow_id: str
    params: dict

@app.get("/")
async def root():
    force_log("Root endpoint called")
    logger.info("Root endpoint called")
    return {"message": "Debug server is running"}

@app.get("/test")
async def test():
    force_log("Test endpoint called")
    logger.info("Test endpoint called")
    return {"message": "Test successful"}

@app.post("/run-workflow")
async def execute_workflow(request: WorkflowRequest):
    force_log(f"Workflow endpoint called with: {request}")
    logger.info(f"Received workflow request: {request}")
    
    try:
        force_log("About to import and call workflow function")
        
        # 简化的工作流调用，避免复杂的依赖
        from coze_workflow_pyq_media import run_coze_workflow
        
        force_log("Successfully imported workflow function")
        
        result = run_coze_workflow(
            workflow_id=request.workflow_id,
            params=request.params
        )
        
        force_log(f"Workflow completed with result: {result}")
        logger.info("Workflow completed successfully")
        
        return {"status": "success", "data": result}
        
    except Exception as e:
        force_log(f"Error in workflow: {str(e)}")
        logger.error(f"Workflow error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    force_log("Starting debug server...")
    logger.info("Starting debug server")
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8002,
        log_level="info",
        access_log=True,
        reload=False
    )
