#!/usr/bin/env python3
"""
测试API接口调用
"""
import requests
import json

def test_api():
    """测试 /run-workflow 接口"""
    url = "http://127.0.0.1:8000/run-workflow"
    
    # 测试数据
    test_data = {
        "workflow_id": "test_workflow_123",
        "params": {
            "test_param": "test_value",
            "number": 42
        }
    }
    
    try:
        print("正在发送请求到:", url)
        print("请求数据:", json.dumps(test_data, indent=2, ensure_ascii=False))
        
        response = requests.post(
            url,
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
    except requests.exceptions.ConnectionError:
        print("连接失败，请确保服务器正在运行")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_api()
