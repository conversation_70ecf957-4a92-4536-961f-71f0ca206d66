#!/usr/bin/env python3
"""
启动服务器的脚本，确保日志正确配置
"""
import logging
import sys
import uvicorn
from main import app

def setup_logging():
    """设置详细的日志配置"""
    # 清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ],
        force=True  # 强制重新配置
    )
    
    # 设置uvicorn的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    
    # 获取应用logger
    logger = logging.getLogger(__name__)
    logger.info("日志系统已配置完成")
    return logger

if __name__ == "__main__":
    logger = setup_logging()
    logger.info("正在启动 FastAPI 应用...")
    
    # 启动服务器
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True
    )
