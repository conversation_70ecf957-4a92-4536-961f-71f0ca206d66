#!/usr/bin/env python3
"""
基本使用示例
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.app.workflow import WorkflowExecutor, run_coze_workflow
from src.config.logger import setup_logging, get_logger

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger = get_logger("example")
    
    logger.info("开始工作流执行示例")
    
    try:
        # 方法1: 使用WorkflowExecutor类
        executor = WorkflowExecutor()
        result1 = executor.execute(
            workflow_id="your_workflow_id_here",
            params={"key": "value"}
        )
        logger.info(f"方法1执行结果: {result1}")
        
        # 方法2: 使用便捷函数
        result2 = run_coze_workflow(
            workflow_id="your_workflow_id_here",
            params={"key": "value"}
        )
        logger.info(f"方法2执行结果: {result2}")
        
    except Exception as e:
        logger.error(f"执行失败: {e}")

if __name__ == "__main__":
    main()
