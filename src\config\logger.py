#!/usr/bin/env python3
"""
FastAPI 日志配置模块
参考: https://www.51cto.com/article/707542.html
"""
import logging
import logging.config
import sys
import os
from datetime import datetime
from pathlib import Path

def setup_logging(log_dir: str = "logs"):
    """
    设置完整的日志配置
    
    Args:
        log_dir: 日志文件目录，默认为 "logs"
    """
    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    log_path = project_root / log_dir
    
    # 创建logs目录
    log_path.mkdir(exist_ok=True)
    
    # 日志配置字典
    LOGGING_CONFIG = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "access": {
                "format": "%(asctime)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "default",
                "stream": "ext://sys.stdout",
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "detailed",
                "filename": str(log_path / "app.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8",
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": str(log_path / "error.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8",
            },
            "access_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "access",
                "filename": str(log_path / "access.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8",
            },
        },
        "loggers": {
            "": {  # root logger
                "level": "INFO",
                "handlers": ["console", "file", "error_file"],
                "propagate": False,
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": ["console", "file", "error_file"],
                "propagate": False,
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console", "access_file"],
                "propagate": False,
            },
            "pyq_auto_publish": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
        },
    }
    
    # 应用日志配置
    logging.config.dictConfig(LOGGING_CONFIG)
    
    # 返回应用logger
    return logging.getLogger("pyq_auto_publish")

def get_logger(name: str = "pyq_auto_publish"):
    """
    获取指定名称的logger
    
    Args:
        name: logger名称，默认为 "pyq_auto_publish"
    
    Returns:
        Logger实例
    """
    return logging.getLogger(name)
