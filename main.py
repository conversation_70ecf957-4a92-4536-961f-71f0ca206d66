from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from coze_workflow_pyq_media import run_coze_workflow
import logging

# Configure basic logging
logging.basicConfig(level=logging.INFO)

app = FastAPI(title="Coze Workflow API", description="API for running Coze workflows")

class WorkflowRequest(BaseModel):
    workflow_id: str
    params: dict

@app.post("/run-workflow")
async def execute_workflow(request: WorkflowRequest):
    logging.info(f"Received request: {request}")
    try:
        result = run_coze_workflow(
            workflow_id=request.workflow_id,
            params=request.params
        )
        logging.info(f"Workflow result: {result}")
        return {"status": "success", "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)