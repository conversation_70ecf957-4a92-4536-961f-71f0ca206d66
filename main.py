from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from coze_workflow_pyq_media import run_coze_workflow
import logging
import sys

def setup_logging():
    """设置日志配置"""
    # 清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ],
        force=True
    )

    # 设置uvicorn相关的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)

    return logging.getLogger(__name__)

# 设置日志
logger = setup_logging()
logger.info("Main module logger initialized")

app = FastAPI(title="Coze Workflow API", description="API for running Coze workflows")

@app.on_event("startup")
async def startup_event():
    """应用启动时的事件处理"""
    global logger
    logger = setup_logging()
    logger.info("FastAPI application started - logging configured")

class WorkflowRequest(BaseModel):
    workflow_id: str
    params: dict

@app.get("/test-logging")
async def test_logging():
    """测试日志功能的简单端点"""
    current_logger = setup_logging()

    print("[FORCE PRINT] Test logging endpoint called!", flush=True)

    with open("api_calls.log", "a", encoding="utf-8") as f:
        f.write(f"[{__import__('datetime').datetime.now()}] Test logging endpoint called\n")
        f.flush()

    current_logger.info("Test logging endpoint was called")

    return {"message": "Test logging successful", "timestamp": str(__import__('datetime').datetime.now())}

@app.post("/run-workflow")
async def execute_workflow(request: WorkflowRequest):
    # 确保日志配置正确
    current_logger = setup_logging()

    # 强制输出到控制台和文件
    print(f"[FORCE PRINT] API called with request: {request}", flush=True)

    # 写入文件日志
    with open("api_calls.log", "a", encoding="utf-8") as f:
        f.write(f"[{__import__('datetime').datetime.now()}] API called with: {request}\n")
        f.flush()

    current_logger.info(f"Received request: {request}")

    try:
        print(f"[FORCE PRINT] About to call run_coze_workflow", flush=True)

        with open("api_calls.log", "a", encoding="utf-8") as f:
            f.write(f"[{__import__('datetime').datetime.now()}] About to call workflow\n")
            f.flush()

        result = run_coze_workflow(
            workflow_id=request.workflow_id,
            params=request.params
        )

        print(f"[FORCE PRINT] Workflow completed with result: {result}", flush=True)

        with open("api_calls.log", "a", encoding="utf-8") as f:
            f.write(f"[{__import__('datetime').datetime.now()}] Workflow completed\n")
            f.flush()

        current_logger.info(f"Workflow result: {result}")
        return {"status": "success", "data": result}

    except Exception as e:
        print(f"[FORCE PRINT] Error occurred: {str(e)}", flush=True)

        with open("api_calls.log", "a", encoding="utf-8") as f:
            f.write(f"[{__import__('datetime').datetime.now()}] Error: {str(e)}\n")
            f.flush()

        current_logger.error(f"Error executing workflow: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))



if __name__ == "__main__":
    import uvicorn
    logger.info("Starting FastAPI application...")

    # 配置uvicorn日志
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            },
            "access": {
                "format": "%(asctime)s - ACCESS - %(message)s",
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
            "access": {
                "formatter": "access",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
        },
        "loggers": {
            "uvicorn": {"handlers": ["default"], "level": "INFO"},
            "uvicorn.error": {"level": "INFO"},
            "uvicorn.access": {"handlers": ["access"], "level": "INFO", "propagate": False},
        },
    }

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_config=log_config,
        access_log=True,
        use_colors=False,
        reload=False  # 禁用reload以确保日志正常工作
    )