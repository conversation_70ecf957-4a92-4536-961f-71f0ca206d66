from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from coze_workflow_pyq_media import run_coze_workflow
import logging
import sys

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# 获取logger实例
logger = logging.getLogger(__name__)

app = FastAPI(title="Coze Workflow API", description="API for running Coze workflows")

class WorkflowRequest(BaseModel):
    workflow_id: str
    params: dict

@app.post("/run-workflow")
async def execute_workflow(request: WorkflowRequest):
    logger.info(f"Received request: {request}")
    try:
        result = run_coze_workflow(
            workflow_id=request.workflow_id,
            params=request.params
        )
        logger.info(f"Workflow result: {result}")
        return {"status": "success", "data": result}
    except Exception as e:
        logger.error(f"Error executing workflow: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))



if __name__ == "__main__":
    import uvicorn
    logger.info("Starting FastAPI application...")
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )