from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from coze_workflow_pyq_media import run_coze_workflow
from logger_config import setup_logging, get_logger
from middleware import LoggingMiddleware

# 设置日志
setup_logging()
logger = get_logger("main")

app = FastAPI(title="Coze Workflow API", description="API for running Coze workflows")

# 添加日志中间件
app.add_middleware(LoggingMiddleware)

@app.on_event("startup")
async def startup_event():
    """应用启动时的事件处理"""
    logger.info("FastAPI application started - logging configured")

class WorkflowRequest(BaseModel):
    workflow_id: str
    params: dict

@app.get("/test-logging")
async def test_logging():
    """测试日志功能的简单端点"""
    logger.info("Test logging endpoint was called")
    logger.debug("This is a debug message")
    logger.warning("This is a warning message")

    return {
        "message": "Test logging successful",
        "timestamp": str(__import__('datetime').datetime.now()),
        "note": "Check logs/app.log for detailed logs"
    }

@app.post("/run-workflow")
async def execute_workflow(request: WorkflowRequest):
    """执行工作流"""
    logger.info(f"Received workflow request: {request}")

    try:
        logger.info(f"Starting workflow execution for ID: {request.workflow_id}")

        result = run_coze_workflow(
            workflow_id=request.workflow_id,
            params=request.params
        )

        logger.info(f"Workflow execution completed successfully")
        logger.debug(f"Workflow result: {result}")

        return {"status": "success", "data": result}

    except Exception as e:
        logger.error(f"Workflow execution failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))



if __name__ == "__main__":
    import uvicorn

    logger.info("Starting FastAPI application...")

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True,
        reload=False  # 禁用reload以确保日志正常工作
    )