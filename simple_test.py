#!/usr/bin/env python3
"""
最简单的测试服务器
"""
from fastapi import FastAPI
import uvicorn
import sys

app = FastAPI()

@app.get("/")
async def root():
    print("ROOT endpoint called!", flush=True)
    return {"message": "Hello World"}

@app.post("/test")
async def test():
    print("TEST endpoint called!", flush=True)
    return {"message": "Test successful"}

if __name__ == "__main__":
    print("Starting simple test server on port 8001...", flush=True)
    uvicorn.run(app, host="127.0.0.1", port=8001, log_level="info")
