# 初次启动步骤：

## 全局安装uv
```
pip install uv
```

## 进入项目目录
```
cd pyq_auto_publish
```

## 激活项目虚拟环境
```
.venv\Scripts\activate
```

## 安装项目依赖
```
uv pip install -r .\requirements.txt
```

## 启动项目
```
uvicorn main:app --reload --log-level info
```

## 显示如下信息，表示项目启动成功
```
INFO:     Will watch for changes in these directories: ['D:\\wample\\coding\\me\\pyq_auto_publish']
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [39184] using StatReload
INFO:     Started server process [31212]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```
