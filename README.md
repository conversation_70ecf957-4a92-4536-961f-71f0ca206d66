# PyQ Auto Publish

Coze Workflow API for PyQ Auto Publish - 一个基于FastAPI的工作流执行服务

## 📁 项目结构

```
pyq_auto_publish/
├── src/                    # 源代码包
│   ├── __init__.py
│   ├── app/               # 应用模块
│   │   ├── __init__.py
│   │   ├── main.py        # FastAPI主应用
│   │   ├── models.py      # 数据模型
│   │   └── workflow.py    # 工作流执行器
│   └── config/            # 配置模块
│       ├── __init__.py
│       ├── logger.py      # 日志配置
│       ├── middleware.py  # 中间件
│       └── token_manager.py # Token管理
├── examples/              # 使用示例
│   ├── basic_usage.py     # 基本使用示例
│   └── api_client.py      # API客户端示例
├── logs/                  # 日志文件目录
├── run_server.py          # 服务器启动脚本
├── setup.py              # 包安装配置
├── requirements.txt       # 依赖列表
└── README.md             # 项目文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 使用pip安装
pip install -r requirements.txt

# 或使用uv安装（推荐）
pip install uv
uv pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件并配置以下环境变量：
注意：private_key.pem为自己的私钥文件，参考我的教程获取自己的私钥文件，放在项目根目录，文件名不要变：[Coze接口令牌仅30天有效期，手动更新繁琐不便？（附源码）](https://mp.weixin.qq.com/s/NOXkvq04rVTQ_pRjrm0JyA)

```env
COZE_JWT_OAUTH_CLIENT_ID=your_client_id
COZE_JWT_OAUTH_PRIVATE_KEY_PATH=./private_key.pem
COZE_JWT_OAUTH_PUBLIC_KEY_ID=your_public_key_id
```

### 3. 启动服务

```bash
# 方法1: 使用启动脚本
python run_server.py

# 方法2: 直接使用uvicorn
uvicorn src.app.main:app --host 0.0.0.0 --port 8000

# 方法3: 开发模式（自动重载）
uvicorn src.app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 验证服务

访问以下URL验证服务是否正常运行：

- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health
- 根路径: http://localhost:8000/

## 📚 使用方法

### 作为包使用

```python
# 导入工作流执行器
from src.app.workflow import WorkflowExecutor, run_coze_workflow
from src.config.logger import setup_logging, get_logger

# 设置日志
setup_logging()
logger = get_logger("my_app")

# 方法1: 使用类
executor = WorkflowExecutor()
result = executor.execute("workflow_id", {"param": "value"})

# 方法2: 使用函数
result = run_coze_workflow("workflow_id", {"param": "value"})
```

### API调用

```python
import requests

# 执行工作流
response = requests.post(
    "http://localhost:8000/run-workflow",
    json={
        "workflow_id": "your_workflow_id",
        "params": {"key": "value"}
    }
)
result = response.json()
```

## 🔧 配置说明

### 日志配置

日志系统支持以下特性：
- 控制台和文件双重输出
- 自动日志轮转（10MB，保留5个备份）
- 详细的请求追踪（包含请求ID）
- 分级日志记录（INFO、WARNING、ERROR）

日志文件位置：
- `logs/app.log` - 应用日志
- `logs/error.log` - 错误日志
- `logs/access.log` - 访问日志

### 环境变量

| 变量名 | 描述 | 必需 |
|--------|------|------|
| `COZE_JWT_OAUTH_CLIENT_ID` | Coze OAuth客户端ID | 是 |
| `COZE_JWT_OAUTH_PRIVATE_KEY_PATH` | 私钥文件路径 | 是 |
| `COZE_JWT_OAUTH_PUBLIC_KEY_ID` | 公钥ID | 是 |

## 🛠️ 开发

### 安装开发依赖

```bash
pip install -e ".[dev]"
```

### 运行测试

```bash
pytest
```

### 代码格式化

```bash
black src/
```

### 类型检查

```bash
mypy src/
```

## 📝 API文档

启动服务后，访问 http://localhost:8000/docs 查看完整的API文档。

### 主要端点

- `GET /` - 根路径，返回API信息
- `GET /health` - 健康检查
- `POST /run-workflow` - 执行工作流
- `GET /test-logging` - 测试日志功能

## 🔍 故障排除

### 常见问题

1. **日志没有输出到控制台**
   - 确保使用了正确的启动方式
   - 检查日志配置是否正确加载

2. **Token认证失败**
   - 检查环境变量配置
   - 确认私钥文件路径正确
   - 验证Coze OAuth配置

3. **工作流执行失败**
   - 检查workflow_id格式
   - 确认参数格式正确
   - 查看详细错误日志

## 📄 许可证

MIT License
