#!/usr/bin/env python3
"""
数据模型定义
"""
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional

class WorkflowRequest(BaseModel):
    """工作流请求模型"""
    workflow_id: str = Field(..., description="工作流ID")
    params: Dict[str, Any] = Field(..., description="工作流参数")

class WorkflowResponse(BaseModel):
    """工作流响应模型"""
    status: str = Field(..., description="执行状态")
    data: Optional[Dict[str, Any]] = Field(None, description="执行结果数据")
    message: Optional[str] = Field(None, description="响应消息")

class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: str = Field(..., description="检查时间")
    version: str = Field(..., description="版本号")
