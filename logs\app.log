2025-06-05 10:42:17 - middleware - INFO - middleware - dispatch - 29 - [d722dfac-957a-4450-a7ec-dd79708a5aa0] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-06-05 10:42:17 - middleware - INFO - middleware - dispatch - 40 - [d722dfac-957a-4450-a7ec-dd79708a5aa0] Request body: {

    "workflow_id": "7510526092345376831",

    "params": {

        "content_start": "闲聊类",

        "disease_name": "高血压",

        "shop_name": "悦和园"

    }

}
2025-06-05 10:42:17 - main - INFO - main - execute_workflow - 76 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '闲聊类', 'disease_name': '高血压', 'shop_name': '悦和园'}
2025-06-05 10:42:17 - main - INFO - main - execute_workflow - 79 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 10:42:17 - workflow - INFO - workflow - execute - 38 - Executing workflow 7510526092345376831 with params: {'content_start': '闲聊类', 'disease_name': '高血压', 'shop_name': '悦和园'}
2025-06-05 10:42:17 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 10:42:17 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 10:42:17 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 10:42:27 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 10:42:27 - workflow - INFO - workflow - execute - 44 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512290103902355465&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 10:42:27 - main - INFO - main - execute_workflow - 86 - Workflow execution completed successfully
2025-06-05 10:42:27 - main - ERROR - main - execute_workflow - 96 - Workflow execution failed: 1 validation error for WorkflowResponse
data
  Input should be a valid dictionary [type=dict_type, input_value=WorkflowRunResult(debug_u...ata='', execute_id=None), input_type=WorkflowRunResult]
    For further information visit https://errors.pydantic.dev/2.10/v/dict_type
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\src\app\main.py", line 89, in execute_workflow
    return WorkflowResponse(
        status="success",
        data=result,
        message="Workflow executed successfully"
    )
  File "C:\ProgramData\miniconda3\Lib\site-packages\pydantic\main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for WorkflowResponse
data
  Input should be a valid dictionary [type=dict_type, input_value=WorkflowRunResult(debug_u...ata='', execute_id=None), input_type=WorkflowRunResult]
    For further information visit https://errors.pydantic.dev/2.10/v/dict_type
2025-06-05 10:42:27 - middleware - INFO - middleware - dispatch - 54 - [d722dfac-957a-4450-a7ec-dd79708a5aa0] Response: 500 - Time: 10.210s
