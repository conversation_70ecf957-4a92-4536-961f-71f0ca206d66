2025-06-04 22:43:56 - middleware - INFO - middleware - dispatch - 29 - [7684ae22-1b2e-445e-bcc6-49ff5285cb1c] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:43:56 - middleware - INFO - middleware - dispatch - 40 - [7684ae22-1b2e-445e-bcc6-49ff5285cb1c] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:43:56 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:43:56 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:43:56 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:43:56 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:43:57 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:43:57 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:43:58 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:43:58 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042243595B34E3DD38133A0B3823, debug_url: https://www.coze.cn/work_flow?execute_id=7512104974790098978&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:43:58 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042243595B34E3DD38133A0B3823, debug_url: https://www.coze.cn/work_flow?execute_id=7512104974790098978&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042243595B34E3DD38133A0B3823, debug_url: https://www.coze.cn/work_flow?execute_id=7512104974790098978&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042243595B34E3DD38133A0B3823, debug_url: https://www.coze.cn/work_flow?execute_id=7512104974790098978&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:43:58 - middleware - INFO - middleware - dispatch - 54 - [7684ae22-1b2e-445e-bcc6-49ff5285cb1c] Response: 500 - Time: 2.153s
2025-06-04 22:43:58 - middleware - INFO - middleware - dispatch - 29 - [240e3bc9-e39f-46eb-9553-e4e76081d49c] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:43:58 - middleware - INFO - middleware - dispatch - 40 - [240e3bc9-e39f-46eb-9553-e4e76081d49c] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:43:58 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:43:58 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:43:58 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:43:58 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:44:00 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:44:00 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:44:00 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:44:00 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244022FB34532B52E821CB956, debug_url: https://www.coze.cn/work_flow?execute_id=7512104986295058451&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:00 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244022FB34532B52E821CB956, debug_url: https://www.coze.cn/work_flow?execute_id=7512104986295058451&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244022FB34532B52E821CB956, debug_url: https://www.coze.cn/work_flow?execute_id=7512104986295058451&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244022FB34532B52E821CB956, debug_url: https://www.coze.cn/work_flow?execute_id=7512104986295058451&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:00 - middleware - INFO - middleware - dispatch - 54 - [240e3bc9-e39f-46eb-9553-e4e76081d49c] Response: 500 - Time: 2.151s
2025-06-04 22:44:01 - middleware - INFO - middleware - dispatch - 29 - [4d805eeb-8f5b-4639-87b0-912bb363cf16] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:44:01 - middleware - INFO - middleware - dispatch - 40 - [4d805eeb-8f5b-4639-87b0-912bb363cf16] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:44:01 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:01 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:44:01 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:01 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:44:02 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:44:02 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:44:03 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:44:03 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244046DD6003D4890BC0F2731, debug_url: https://www.coze.cn/work_flow?execute_id=7512105003243274274&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:03 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244046DD6003D4890BC0F2731, debug_url: https://www.coze.cn/work_flow?execute_id=7512105003243274274&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244046DD6003D4890BC0F2731, debug_url: https://www.coze.cn/work_flow?execute_id=7512105003243274274&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244046DD6003D4890BC0F2731, debug_url: https://www.coze.cn/work_flow?execute_id=7512105003243274274&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:03 - middleware - INFO - middleware - dispatch - 54 - [4d805eeb-8f5b-4639-87b0-912bb363cf16] Response: 500 - Time: 2.217s
2025-06-04 22:44:04 - middleware - INFO - middleware - dispatch - 29 - [5c13ed2a-9b3e-4427-8f3c-c5a37013fe24] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:44:04 - middleware - INFO - middleware - dispatch - 40 - [5c13ed2a-9b3e-4427-8f3c-c5a37013fe24] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:44:04 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:04 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:44:04 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:04 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:44:05 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:44:05 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:44:06 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:44:06 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422440734C90240D3BE43310453, debug_url: https://www.coze.cn/work_flow?execute_id=7512105013724921919&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:06 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422440734C90240D3BE43310453, debug_url: https://www.coze.cn/work_flow?execute_id=7512105013724921919&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422440734C90240D3BE43310453, debug_url: https://www.coze.cn/work_flow?execute_id=7512105013724921919&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422440734C90240D3BE43310453, debug_url: https://www.coze.cn/work_flow?execute_id=7512105013724921919&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:06 - middleware - INFO - middleware - dispatch - 54 - [5c13ed2a-9b3e-4427-8f3c-c5a37013fe24] Response: 500 - Time: 2.397s
2025-06-04 22:44:07 - middleware - INFO - middleware - dispatch - 29 - [fe547d9b-559e-426e-95bd-19c6570aaa9f] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:44:07 - middleware - INFO - middleware - dispatch - 40 - [fe547d9b-559e-426e-95bd-19c6570aaa9f] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:44:07 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:07 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:44:07 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:07 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:44:08 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:44:08 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:44:09 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:44:09 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224410B9AA2BAF026A1DAA89D0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105030649643046&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:09 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224410B9AA2BAF026A1DAA89D0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105030649643046&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224410B9AA2BAF026A1DAA89D0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105030649643046&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224410B9AA2BAF026A1DAA89D0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105030649643046&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:09 - middleware - INFO - middleware - dispatch - 54 - [fe547d9b-559e-426e-95bd-19c6570aaa9f] Response: 500 - Time: 2.375s
2025-06-04 22:44:10 - middleware - INFO - middleware - dispatch - 29 - [a618df43-d542-490b-8e8a-f31f37e0ba86] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:44:10 - middleware - INFO - middleware - dispatch - 40 - [a618df43-d542-490b-8e8a-f31f37e0ba86] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:44:10 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:10 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:44:10 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:10 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:44:11 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:44:11 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:44:12 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:44:12 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224413BC2A47401EBA8BE0A20A, debug_url: https://www.coze.cn/work_flow?execute_id=7512105031010779151&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:12 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224413BC2A47401EBA8BE0A20A, debug_url: https://www.coze.cn/work_flow?execute_id=7512105031010779151&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224413BC2A47401EBA8BE0A20A, debug_url: https://www.coze.cn/work_flow?execute_id=7512105031010779151&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224413BC2A47401EBA8BE0A20A, debug_url: https://www.coze.cn/work_flow?execute_id=7512105031010779151&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:12 - middleware - INFO - middleware - dispatch - 54 - [a618df43-d542-490b-8e8a-f31f37e0ba86] Response: 500 - Time: 2.235s
2025-06-04 22:44:12 - middleware - INFO - middleware - dispatch - 29 - [55183fe2-eff4-463c-988a-a11917e5f0b8] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:44:12 - middleware - INFO - middleware - dispatch - 40 - [55183fe2-eff4-463c-988a-a11917e5f0b8] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:44:12 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:12 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:44:12 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:12 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:44:14 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:44:14 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:44:15 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:44:15 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244160E303E245B5CFC0C9273, debug_url: https://www.coze.cn/work_flow?execute_id=7512105044574896180&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:15 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244160E303E245B5CFC0C9273, debug_url: https://www.coze.cn/work_flow?execute_id=7512105044574896180&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244160E303E245B5CFC0C9273, debug_url: https://www.coze.cn/work_flow?execute_id=7512105044574896180&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244160E303E245B5CFC0C9273, debug_url: https://www.coze.cn/work_flow?execute_id=7512105044574896180&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:15 - middleware - INFO - middleware - dispatch - 54 - [55183fe2-eff4-463c-988a-a11917e5f0b8] Response: 500 - Time: 2.387s
2025-06-04 22:44:15 - middleware - INFO - middleware - dispatch - 29 - [7475b13a-c299-4268-9601-b5fb89412af7] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:44:15 - middleware - INFO - middleware - dispatch - 40 - [7475b13a-c299-4268-9601-b5fb89412af7] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:44:15 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:15 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:44:15 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:15 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:44:16 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:44:16 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:44:17 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:44:17 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224419D972CBFCE808FF255DD2, debug_url: https://www.coze.cn/work_flow?execute_id=7512105065093513231&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:17 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224419D972CBFCE808FF255DD2, debug_url: https://www.coze.cn/work_flow?execute_id=7512105065093513231&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224419D972CBFCE808FF255DD2, debug_url: https://www.coze.cn/work_flow?execute_id=7512105065093513231&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224419D972CBFCE808FF255DD2, debug_url: https://www.coze.cn/work_flow?execute_id=7512105065093513231&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:17 - middleware - INFO - middleware - dispatch - 54 - [7475b13a-c299-4268-9601-b5fb89412af7] Response: 500 - Time: 2.283s
2025-06-04 22:44:18 - middleware - INFO - middleware - dispatch - 29 - [61d18633-dbd3-42ec-bc31-45a710518cbd] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:44:18 - middleware - INFO - middleware - dispatch - 40 - [61d18633-dbd3-42ec-bc31-45a710518cbd] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:44:18 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:18 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:44:18 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:18 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:44:19 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:44:19 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:44:20 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:44:20 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224421DA0B1A1BC85EA6FE8CF6, debug_url: https://www.coze.cn/work_flow?execute_id=7512105074710741027&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:20 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224421DA0B1A1BC85EA6FE8CF6, debug_url: https://www.coze.cn/work_flow?execute_id=7512105074710741027&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224421DA0B1A1BC85EA6FE8CF6, debug_url: https://www.coze.cn/work_flow?execute_id=7512105074710741027&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224421DA0B1A1BC85EA6FE8CF6, debug_url: https://www.coze.cn/work_flow?execute_id=7512105074710741027&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:20 - middleware - INFO - middleware - dispatch - 54 - [61d18633-dbd3-42ec-bc31-45a710518cbd] Response: 500 - Time: 2.371s
2025-06-04 22:44:21 - middleware - INFO - middleware - dispatch - 29 - [ed59d110-02ad-4051-8fde-2a77225b7b12] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:44:21 - middleware - INFO - middleware - dispatch - 40 - [ed59d110-02ad-4051-8fde-2a77225b7b12] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:44:21 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:21 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:44:21 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:44:21 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:44:22 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:44:22 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:44:23 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:44:23 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224424B97DF35E11762139E13F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105082797522998&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:23 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224424B97DF35E11762139E13F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105082797522998&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224424B97DF35E11762139E13F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105082797522998&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224424B97DF35E11762139E13F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105082797522998&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:23 - middleware - INFO - middleware - dispatch - 54 - [ed59d110-02ad-4051-8fde-2a77225b7b12] Response: 500 - Time: 2.416s
2025-06-04 22:44:47 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-04 22:44:47 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-04 22:44:47 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-04 22:44:47 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [75720]
2025-06-04 22:44:48 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-04 22:44:48 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-04 22:44:48 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-04 22:44:48 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [71520]
2025-06-04 22:44:48 - uvicorn.error - INFO - server - _serve - 83 - Started server process [77648]
2025-06-04 22:44:48 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-04 22:44:48 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-04 22:44:48 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-04 22:44:49 - uvicorn.error - INFO - server - _serve - 83 - Started server process [81808]
2025-06-04 22:44:49 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-04 22:44:49 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-04 22:44:49 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-04 22:44:49 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-04 22:44:49 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-04 22:44:49 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-04 22:44:49 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [77648]
2025-06-04 22:44:51 - uvicorn.error - INFO - server - _serve - 83 - Started server process [76784]
2025-06-04 22:44:51 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-04 22:44:51 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-04 22:44:51 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-04 22:45:31 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-04 22:45:31 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-04 22:45:31 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-04 22:45:31 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [76784]
2025-06-04 22:45:31 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-04 22:45:31 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-04 22:45:31 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-04 22:45:31 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [81808]
2025-06-04 22:45:32 - uvicorn.error - INFO - server - _serve - 83 - Started server process [20324]
2025-06-04 22:45:32 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-04 22:45:32 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-04 22:45:32 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-04 22:45:32 - uvicorn.error - INFO - server - _serve - 83 - Started server process [79020]
2025-06-04 22:45:32 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-04 22:45:32 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-04 22:45:32 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-04 22:46:12 - middleware - INFO - middleware - dispatch - 29 - [25f4268a-9adc-45c2-a483-0a80786824a2] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:46:12 - middleware - INFO - middleware - dispatch - 40 - [25f4268a-9adc-45c2-a483-0a80786824a2] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:46:12 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:46:12 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:46:12 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:46:12 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:46:13 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:46:13 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:46:14 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:46:14 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224615937C2D792E470A5F4CB8, debug_url: https://www.coze.cn/work_flow?execute_id=7512105554011013183&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:14 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224615937C2D792E470A5F4CB8, debug_url: https://www.coze.cn/work_flow?execute_id=7512105554011013183&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224615937C2D792E470A5F4CB8, debug_url: https://www.coze.cn/work_flow?execute_id=7512105554011013183&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224615937C2D792E470A5F4CB8, debug_url: https://www.coze.cn/work_flow?execute_id=7512105554011013183&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:14 - middleware - INFO - middleware - dispatch - 54 - [25f4268a-9adc-45c2-a483-0a80786824a2] Response: 500 - Time: 2.285s
2025-06-04 22:46:14 - middleware - INFO - middleware - dispatch - 29 - [1383d2ca-208f-4634-832d-c07b7af58f73] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:46:14 - middleware - INFO - middleware - dispatch - 40 - [1383d2ca-208f-4634-832d-c07b7af58f73] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:46:14 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:46:14 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:46:14 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:46:14 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:46:16 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:46:16 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:46:17 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:46:17 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422461893DE5B6CCD9B448DEC26, debug_url: https://www.coze.cn/work_flow?execute_id=7512105573866012735&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:17 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422461893DE5B6CCD9B448DEC26, debug_url: https://www.coze.cn/work_flow?execute_id=7512105573866012735&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422461893DE5B6CCD9B448DEC26, debug_url: https://www.coze.cn/work_flow?execute_id=7512105573866012735&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422461893DE5B6CCD9B448DEC26, debug_url: https://www.coze.cn/work_flow?execute_id=7512105573866012735&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:17 - middleware - INFO - middleware - dispatch - 54 - [1383d2ca-208f-4634-832d-c07b7af58f73] Response: 500 - Time: 2.088s
2025-06-04 22:46:17 - middleware - INFO - middleware - dispatch - 29 - [c2b1e960-b303-422d-8ffd-8eb08315638f] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:46:17 - middleware - INFO - middleware - dispatch - 40 - [c2b1e960-b303-422d-8ffd-8eb08315638f] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:46:17 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:46:17 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:46:17 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:46:17 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:46:18 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:46:18 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:46:19 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:46:19 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224620732E6C95D1BF941DF9E0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105582658355235&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:19 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224620732E6C95D1BF941DF9E0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105582658355235&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224620732E6C95D1BF941DF9E0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105582658355235&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224620732E6C95D1BF941DF9E0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105582658355235&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:19 - middleware - INFO - middleware - dispatch - 54 - [c2b1e960-b303-422d-8ffd-8eb08315638f] Response: 500 - Time: 2.201s
2025-06-04 22:46:20 - middleware - INFO - middleware - dispatch - 29 - [14a48772-486d-4fad-9007-5a55f2d1731c] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:46:20 - middleware - INFO - middleware - dispatch - 40 - [14a48772-486d-4fad-9007-5a55f2d1731c] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:46:20 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:46:20 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:46:20 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:46:20 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:46:21 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:46:21 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:46:22 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:46:22 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042246232FE347D8D998242DC0BF, debug_url: https://www.coze.cn/work_flow?execute_id=7512105601443708962&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:22 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042246232FE347D8D998242DC0BF, debug_url: https://www.coze.cn/work_flow?execute_id=7512105601443708962&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042246232FE347D8D998242DC0BF, debug_url: https://www.coze.cn/work_flow?execute_id=7512105601443708962&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042246232FE347D8D998242DC0BF, debug_url: https://www.coze.cn/work_flow?execute_id=7512105601443708962&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:22 - middleware - INFO - middleware - dispatch - 54 - [14a48772-486d-4fad-9007-5a55f2d1731c] Response: 500 - Time: 2.095s
2025-06-04 22:47:51 - middleware - INFO - middleware - dispatch - 29 - [278bc46c-3c28-4909-b510-5e478673a343] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:47:51 - middleware - INFO - middleware - dispatch - 40 - [278bc46c-3c28-4909-b510-5e478673a343] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:47:51 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:47:51 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:47:51 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:47:51 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:47:52 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:47:52 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:47:53 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:47:53 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224754D9F146E33D6E64B29E6F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105996391907379&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:53 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224754D9F146E33D6E64B29E6F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105996391907379&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224754D9F146E33D6E64B29E6F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105996391907379&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224754D9F146E33D6E64B29E6F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105996391907379&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:53 - middleware - INFO - middleware - dispatch - 54 - [278bc46c-3c28-4909-b510-5e478673a343] Response: 500 - Time: 2.259s
2025-06-04 22:47:54 - middleware - INFO - middleware - dispatch - 29 - [eed059dc-6c6e-45f3-bc44-29f9034fe70b] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:47:54 - middleware - INFO - middleware - dispatch - 40 - [eed059dc-6c6e-45f3-bc44-29f9034fe70b] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:47:54 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:47:54 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:47:54 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:47:54 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:47:55 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:47:55 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:47:56 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:47:56 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422475769108196F78225AACFE1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106001509367834&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:56 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422475769108196F78225AACFE1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106001509367834&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422475769108196F78225AACFE1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106001509367834&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422475769108196F78225AACFE1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106001509367834&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:56 - middleware - INFO - middleware - dispatch - 54 - [eed059dc-6c6e-45f3-bc44-29f9034fe70b] Response: 500 - Time: 2.345s
2025-06-04 22:47:57 - middleware - INFO - middleware - dispatch - 29 - [1eab704a-2bfb-4a0b-9bbb-7e1e896b6d36] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:47:57 - middleware - INFO - middleware - dispatch - 40 - [1eab704a-2bfb-4a0b-9bbb-7e1e896b6d36] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": null, "disease_name": null, "shop_name": null}}
2025-06-04 22:47:57 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:47:57 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:47:57 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': None, 'disease_name': None, 'shop_name': None}
2025-06-04 22:47:57 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:47:58 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:47:58 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:47:59 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:47:59 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224800F05A36FFA6DA3298BDA1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106020496523302&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:59 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224800F05A36FFA6DA3298BDA1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106020496523302&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224800F05A36FFA6DA3298BDA1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106020496523302&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224800F05A36FFA6DA3298BDA1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106020496523302&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:59 - middleware - INFO - middleware - dispatch - 54 - [1eab704a-2bfb-4a0b-9bbb-7e1e896b6d36] Response: 500 - Time: 2.411s
2025-06-04 22:49:02 - middleware - INFO - middleware - dispatch - 29 - [ad49c284-a6b5-496c-b053-aea37d705b0f] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:49:02 - middleware - INFO - middleware - dispatch - 40 - [ad49c284-a6b5-496c-b053-aea37d705b0f] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u60c5\u7eea\u7c7b", "disease_name": "\u6c14\u8840\u4e0d\u8db3", "shop_name": "\u6587\u5316\u8425"}}
2025-06-04 22:49:02 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '情绪类', 'disease_name': '气血不足', 'shop_name': '文化营'}
2025-06-04 22:49:02 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:49:02 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '情绪类', 'disease_name': '气血不足', 'shop_name': '文化营'}
2025-06-04 22:49:02 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:49:03 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:49:03 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:49:19 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:49:19 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106302143856650&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:49:19 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:49:19 - middleware - INFO - middleware - dispatch - 54 - [ad49c284-a6b5-496c-b053-aea37d705b0f] Response: 200 - Time: 17.472s
2025-06-04 22:49:20 - middleware - INFO - middleware - dispatch - 29 - [0737869c-ec0b-4a9c-a83c-f23dc686e559] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:49:20 - middleware - INFO - middleware - dispatch - 40 - [0737869c-ec0b-4a9c-a83c-f23dc686e559] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u95f2\u804a\u7c7b", "disease_name": "\u6c14\u8840\u4e0d\u8db3", "shop_name": "\u5174\u6d77\u56ed"}}
2025-06-04 22:49:20 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '闲聊类', 'disease_name': '气血不足', 'shop_name': '兴海园'}
2025-06-04 22:49:20 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:49:20 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '闲聊类', 'disease_name': '气血不足', 'shop_name': '兴海园'}
2025-06-04 22:49:20 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:49:21 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:49:21 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:49:36 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:49:36 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106379173036042&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:49:36 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:49:36 - middleware - INFO - middleware - dispatch - 54 - [0737869c-ec0b-4a9c-a83c-f23dc686e559] Response: 200 - Time: 16.486s
2025-06-04 22:49:37 - middleware - INFO - middleware - dispatch - 29 - [f8c427ec-e07f-4629-8a9a-2deaa0ecb7f3] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:49:37 - middleware - INFO - middleware - dispatch - 40 - [f8c427ec-e07f-4629-8a9a-2deaa0ecb7f3] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u95ee\u9898\u7c7b", "disease_name": "\u9ad8\u8840\u538b", "shop_name": "\u6587\u5316\u8425"}}
2025-06-04 22:49:37 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '问题类', 'disease_name': '高血压', 'shop_name': '文化营'}
2025-06-04 22:49:37 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:49:37 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '问题类', 'disease_name': '高血压', 'shop_name': '文化营'}
2025-06-04 22:49:37 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:49:38 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:49:38 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:49:48 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:49:48 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106436677140520&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:49:48 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:49:48 - middleware - INFO - middleware - dispatch - 54 - [f8c427ec-e07f-4629-8a9a-2deaa0ecb7f3] Response: 200 - Time: 11.582s
2025-06-04 22:49:49 - middleware - INFO - middleware - dispatch - 29 - [64dbe9bd-1a10-4c2c-a147-0e8a835ad186] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:49:49 - middleware - INFO - middleware - dispatch - 40 - [64dbe9bd-1a10-4c2c-a147-0e8a835ad186] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u65f6\u95f4\u5207\u5165\u7c7b", "disease_name": "\u9aa8\u8d28\u758f\u677e", "shop_name": "\u5e84\u5b50\u8425"}}
2025-06-04 22:49:49 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '时间切入类', 'disease_name': '骨质疏松', 'shop_name': '庄子营'}
2025-06-04 22:49:49 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:49:49 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '时间切入类', 'disease_name': '骨质疏松', 'shop_name': '庄子营'}
2025-06-04 22:49:49 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:49:50 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:49:50 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:50:00 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:50:00 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106498978414611&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:50:00 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:50:00 - middleware - INFO - middleware - dispatch - 54 - [64dbe9bd-1a10-4c2c-a147-0e8a835ad186] Response: 200 - Time: 11.059s
2025-06-04 22:50:00 - middleware - INFO - middleware - dispatch - 29 - [19ccc24e-4a6b-4534-813f-814c1c5b880d] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:50:00 - middleware - INFO - middleware - dispatch - 40 - [19ccc24e-4a6b-4534-813f-814c1c5b880d] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u4e60\u60ef\u7c7b", "disease_name": "\u9aa8\u8d28\u758f\u677e", "shop_name": "\u65fa\u89d2"}}
2025-06-04 22:50:00 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '习惯类', 'disease_name': '骨质疏松', 'shop_name': '旺角'}
2025-06-04 22:50:00 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:50:00 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '习惯类', 'disease_name': '骨质疏松', 'shop_name': '旺角'}
2025-06-04 22:50:00 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:50:02 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:50:02 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:50:17 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:50:17 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106543535112246&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:50:17 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:50:17 - middleware - INFO - middleware - dispatch - 54 - [19ccc24e-4a6b-4534-813f-814c1c5b880d] Response: 200 - Time: 16.306s
2025-06-04 22:50:17 - middleware - INFO - middleware - dispatch - 29 - [de859a29-0a71-4d5f-837c-1348440157c5] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:50:17 - middleware - INFO - middleware - dispatch - 40 - [de859a29-0a71-4d5f-837c-1348440157c5] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u60c5\u666f\u63cf\u5199\u7c7b", "disease_name": "\u9aa8\u8d28\u758f\u677e", "shop_name": "\u5e84\u5b50\u8425"}}
2025-06-04 22:50:17 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '情景描写类', 'disease_name': '骨质疏松', 'shop_name': '庄子营'}
2025-06-04 22:50:17 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:50:17 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '情景描写类', 'disease_name': '骨质疏松', 'shop_name': '庄子营'}
2025-06-04 22:50:17 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:50:18 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:50:18 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:50:19 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-04 22:50:19 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-04 22:50:19 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-04 22:50:19 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [79020]
2025-06-04 22:50:21 - uvicorn.error - INFO - server - _serve - 83 - Started server process [51776]
2025-06-04 22:50:21 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-04 22:50:21 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-04 22:50:21 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-04 22:50:21 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-04 22:50:21 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-04 22:50:21 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-04 22:50:21 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [51776]
2025-06-04 22:50:22 - uvicorn.error - INFO - server - _serve - 83 - Started server process [67184]
2025-06-04 22:50:22 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-04 22:50:22 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-04 22:50:22 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-04 22:50:31 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:50:31 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106619699806248&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:50:31 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:50:31 - middleware - INFO - middleware - dispatch - 54 - [de859a29-0a71-4d5f-837c-1348440157c5] Response: 200 - Time: 13.905s
2025-06-04 22:50:31 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-04 22:50:31 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-04 22:50:31 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-04 22:50:31 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [20324]
2025-06-04 22:50:32 - uvicorn.error - INFO - server - _serve - 83 - Started server process [74816]
2025-06-04 22:50:32 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-04 22:50:32 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-04 22:50:32 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-04 22:50:32 - middleware - INFO - middleware - dispatch - 29 - [85bdb398-6de3-4573-b004-c5310421da63] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:50:32 - middleware - INFO - middleware - dispatch - 40 - [85bdb398-6de3-4573-b004-c5310421da63] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u4e60\u60ef\u7c7b", "disease_name": "\u5931\u7720", "shop_name": "\u5174\u6d77\u56ed"}}
2025-06-04 22:50:32 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '习惯类', 'disease_name': '失眠', 'shop_name': '兴海园'}
2025-06-04 22:50:32 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:50:32 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '习惯类', 'disease_name': '失眠', 'shop_name': '兴海园'}
2025-06-04 22:50:32 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:50:32 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:50:32 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:50:39 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:50:39 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106674540478503&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:50:39 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:50:39 - middleware - INFO - middleware - dispatch - 54 - [85bdb398-6de3-4573-b004-c5310421da63] Response: 200 - Time: 6.907s
2025-06-04 22:50:39 - middleware - INFO - middleware - dispatch - 29 - [d0a8c362-bf0e-4e7f-9376-ed896a4c5fa8] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:50:39 - middleware - INFO - middleware - dispatch - 40 - [d0a8c362-bf0e-4e7f-9376-ed896a4c5fa8] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u95ee\u9898\u7c7b", "disease_name": "\u9aa8\u8d28\u758f\u677e", "shop_name": "\u65fa\u89d2"}}
2025-06-04 22:50:39 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '问题类', 'disease_name': '骨质疏松', 'shop_name': '旺角'}
2025-06-04 22:50:39 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:50:39 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '问题类', 'disease_name': '骨质疏松', 'shop_name': '旺角'}
2025-06-04 22:50:39 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:50:40 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:50:40 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:50:55 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:50:55 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106718890164274&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:50:55 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:50:55 - middleware - INFO - middleware - dispatch - 54 - [d0a8c362-bf0e-4e7f-9376-ed896a4c5fa8] Response: 200 - Time: 15.768s
2025-06-04 22:50:55 - middleware - INFO - middleware - dispatch - 29 - [71c76bfb-2f7e-428c-88a5-c972e69e7645] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:50:55 - middleware - INFO - middleware - dispatch - 40 - [71c76bfb-2f7e-428c-88a5-c972e69e7645] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u65f6\u95f4\u5207\u5165\u7c7b", "disease_name": "\u6c14\u8840\u4e0d\u8db3", "shop_name": "\u6587\u5316\u8425"}}
2025-06-04 22:50:55 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '时间切入类', 'disease_name': '气血不足', 'shop_name': '文化营'}
2025-06-04 22:50:55 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:50:55 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '时间切入类', 'disease_name': '气血不足', 'shop_name': '文化营'}
2025-06-04 22:50:55 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:50:57 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:50:57 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:51:08 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:51:08 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106789065031718&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:51:08 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:51:08 - middleware - INFO - middleware - dispatch - 54 - [71c76bfb-2f7e-428c-88a5-c972e69e7645] Response: 200 - Time: 12.973s
2025-06-04 22:51:09 - middleware - INFO - middleware - dispatch - 29 - [b34b918f-964c-47bc-a41f-ef2d4668c72f] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:51:09 - middleware - INFO - middleware - dispatch - 40 - [b34b918f-964c-47bc-a41f-ef2d4668c72f] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u95ee\u9898\u7c7b", "disease_name": "\u9ad8\u8840\u538b", "shop_name": "\u5b89\u5b81\u534e\u5ead"}}
2025-06-04 22:51:09 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '问题类', 'disease_name': '高血压', 'shop_name': '安宁华庭'}
2025-06-04 22:51:09 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:51:09 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '问题类', 'disease_name': '高血压', 'shop_name': '安宁华庭'}
2025-06-04 22:51:09 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:51:10 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:51:10 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:51:23 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:51:23 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106829762789412&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:51:23 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:51:23 - middleware - INFO - middleware - dispatch - 54 - [b34b918f-964c-47bc-a41f-ef2d4668c72f] Response: 200 - Time: 13.902s
2025-06-04 22:51:23 - middleware - INFO - middleware - dispatch - 29 - [41a40c8c-2bac-4f05-bebf-228121939e7d] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:51:23 - middleware - INFO - middleware - dispatch - 40 - [41a40c8c-2bac-4f05-bebf-228121939e7d] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u60c5\u7eea\u7c7b", "disease_name": "\u5931\u7720", "shop_name": "\u5e84\u5b50\u8425"}}
2025-06-04 22:51:23 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '情绪类', 'disease_name': '失眠', 'shop_name': '庄子营'}
2025-06-04 22:51:23 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:51:23 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '情绪类', 'disease_name': '失眠', 'shop_name': '庄子营'}
2025-06-04 22:51:23 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:51:24 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:51:24 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:51:38 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:51:38 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106897577082932&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:51:38 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:51:38 - middleware - INFO - middleware - dispatch - 54 - [41a40c8c-2bac-4f05-bebf-228121939e7d] Response: 200 - Time: 15.320s
2025-06-04 22:51:39 - middleware - INFO - middleware - dispatch - 29 - [e415ae16-cdbd-431c-a660-82af8beb23a2] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:51:39 - middleware - INFO - middleware - dispatch - 40 - [e415ae16-cdbd-431c-a660-82af8beb23a2] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u95f2\u804a\u7c7b", "disease_name": "\u9aa8\u8d28\u758f\u677e", "shop_name": "\u6587\u5316\u8425"}}
2025-06-04 22:51:39 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '闲聊类', 'disease_name': '骨质疏松', 'shop_name': '文化营'}
2025-06-04 22:51:39 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:51:39 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '闲聊类', 'disease_name': '骨质疏松', 'shop_name': '文化营'}
2025-06-04 22:51:39 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:51:40 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:51:40 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:51:51 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:51:51 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512106971680522274&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:51:51 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:51:51 - middleware - INFO - middleware - dispatch - 54 - [e415ae16-cdbd-431c-a660-82af8beb23a2] Response: 200 - Time: 11.613s
2025-06-04 22:51:51 - middleware - INFO - middleware - dispatch - 29 - [0b4e8708-6ddf-4ab3-ad4e-4d50ef20eb06] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:51:51 - middleware - INFO - middleware - dispatch - 40 - [0b4e8708-6ddf-4ab3-ad4e-4d50ef20eb06] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u5185\u5fc3\u72ec\u767d\u7c7b", "disease_name": "\u6c14\u8840\u4e0d\u8db3", "shop_name": "\u5b89\u5b81\u534e\u5ead"}}
2025-06-04 22:51:51 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '内心独白类', 'disease_name': '气血不足', 'shop_name': '安宁华庭'}
2025-06-04 22:51:51 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:51:51 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '内心独白类', 'disease_name': '气血不足', 'shop_name': '安宁华庭'}
2025-06-04 22:51:51 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:51:52 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:51:52 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:52:05 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:52:05 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512107018934435875&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:52:05 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:52:05 - middleware - INFO - middleware - dispatch - 54 - [0b4e8708-6ddf-4ab3-ad4e-4d50ef20eb06] Response: 200 - Time: 13.610s
2025-06-04 22:52:05 - middleware - INFO - middleware - dispatch - 29 - [15ff06d2-434a-4b84-adfa-19328790b281] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:52:05 - middleware - INFO - middleware - dispatch - 40 - [15ff06d2-434a-4b84-adfa-19328790b281] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u60c5\u7eea\u7c7b", "disease_name": "\u9ad8\u8840\u538b", "shop_name": "\u5e84\u5b50\u8425"}}
2025-06-04 22:52:05 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '情绪类', 'disease_name': '高血压', 'shop_name': '庄子营'}
2025-06-04 22:52:05 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:52:05 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '情绪类', 'disease_name': '高血压', 'shop_name': '庄子营'}
2025-06-04 22:52:05 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:52:07 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:52:07 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:52:21 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:52:21 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512107078522945572&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:52:21 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:52:21 - middleware - INFO - middleware - dispatch - 54 - [15ff06d2-434a-4b84-adfa-19328790b281] Response: 200 - Time: 15.464s
2025-06-04 22:52:21 - middleware - INFO - middleware - dispatch - 29 - [d2cb06c4-3459-43ec-8e9a-07c4c5a24b07] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-04 22:52:21 - middleware - INFO - middleware - dispatch - 40 - [d2cb06c4-3459-43ec-8e9a-07c4c5a24b07] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u65f6\u95f4\u5207\u5165\u7c7b", "disease_name": "\u6c14\u8840\u4e0d\u8db3", "shop_name": "\u60a6\u548c\u56ed"}}
2025-06-04 22:52:21 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '时间切入类', 'disease_name': '气血不足', 'shop_name': '悦和园'}
2025-06-04 22:52:21 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-04 22:52:21 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '时间切入类', 'disease_name': '气血不足', 'shop_name': '悦和园'}
2025-06-04 22:52:21 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-04 22:52:22 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:52:22 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-04 22:52:34 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:52:34 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512107149524762658&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-04 22:52:34 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-04 22:52:34 - middleware - INFO - middleware - dispatch - 54 - [d2cb06c4-3459-43ec-8e9a-07c4c5a24b07] Response: 200 - Time: 13.317s
2025-06-05 09:43:11 - middleware - INFO - middleware - dispatch - 29 - [c0b2a5e2-d652-4947-a2f2-d126b3ea2029] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-05 09:43:11 - middleware - INFO - middleware - dispatch - 40 - [c0b2a5e2-d652-4947-a2f2-d126b3ea2029] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u95ee\u9898\u7c7b", "disease_name": "\u9ad8\u8840\u538b", "shop_name": "\u5b89\u5b81\u534e\u5ead"}}
2025-06-05 09:43:11 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '问题类', 'disease_name': '高血压', 'shop_name': '安宁华庭'}
2025-06-05 09:43:11 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:43:11 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '问题类', 'disease_name': '高血压', 'shop_name': '安宁华庭'}
2025-06-05 09:43:11 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:43:12 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:43:12 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:43:24 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:43:24 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512274877186326554&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:43:24 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:43:24 - middleware - INFO - middleware - dispatch - 54 - [c0b2a5e2-d652-4947-a2f2-d126b3ea2029] Response: 200 - Time: 12.999s
2025-06-05 09:43:24 - middleware - INFO - middleware - dispatch - 29 - [941ea48d-cc1b-4f33-951e-44817292b16c] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-05 09:43:24 - middleware - INFO - middleware - dispatch - 40 - [941ea48d-cc1b-4f33-951e-44817292b16c] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u65f6\u95f4\u5207\u5165\u7c7b", "disease_name": "\u5931\u7720", "shop_name": "\u7428\u5ead"}}
2025-06-05 09:43:24 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '时间切入类', 'disease_name': '失眠', 'shop_name': '琨庭'}
2025-06-05 09:43:24 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:43:24 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '时间切入类', 'disease_name': '失眠', 'shop_name': '琨庭'}
2025-06-05 09:43:24 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:43:25 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:43:25 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:43:37 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:43:37 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512274925116882979&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:43:37 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:43:37 - middleware - INFO - middleware - dispatch - 54 - [941ea48d-cc1b-4f33-951e-44817292b16c] Response: 200 - Time: 12.315s
2025-06-05 09:43:37 - middleware - INFO - middleware - dispatch - 29 - [68271d02-4c51-4a8a-874f-3fd8529f70a0] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-05 09:43:37 - middleware - INFO - middleware - dispatch - 40 - [68271d02-4c51-4a8a-874f-3fd8529f70a0] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u5185\u5fc3\u72ec\u767d\u7c7b", "disease_name": "\u9ad8\u8840\u538b", "shop_name": "\u5174\u6d77\u56ed"}}
2025-06-05 09:43:37 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '内心独白类', 'disease_name': '高血压', 'shop_name': '兴海园'}
2025-06-05 09:43:37 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:43:37 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '内心独白类', 'disease_name': '高血压', 'shop_name': '兴海园'}
2025-06-05 09:43:37 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:43:38 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:43:38 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:43:49 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:43:49 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512274989384073231&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:43:49 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:43:49 - middleware - INFO - middleware - dispatch - 54 - [68271d02-4c51-4a8a-874f-3fd8529f70a0] Response: 200 - Time: 11.651s
2025-06-05 09:43:49 - middleware - INFO - middleware - dispatch - 29 - [427b383f-dfef-4b78-9914-0cf4a95ddd60] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-05 09:43:49 - middleware - INFO - middleware - dispatch - 40 - [427b383f-dfef-4b78-9914-0cf4a95ddd60] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u65f6\u95f4\u5207\u5165\u7c7b", "disease_name": "\u5931\u7720", "shop_name": "\u7428\u5ead"}}
2025-06-05 09:43:49 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '时间切入类', 'disease_name': '失眠', 'shop_name': '琨庭'}
2025-06-05 09:43:49 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:43:49 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '时间切入类', 'disease_name': '失眠', 'shop_name': '琨庭'}
2025-06-05 09:43:49 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:43:50 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:43:50 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:44:03 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:44:03 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512275039963758618&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:44:03 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:44:03 - middleware - INFO - middleware - dispatch - 54 - [427b383f-dfef-4b78-9914-0cf4a95ddd60] Response: 200 - Time: 13.654s
2025-06-05 09:44:03 - middleware - INFO - middleware - dispatch - 29 - [f3fa7c25-3a6e-491d-8d2f-be751c144350] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-05 09:44:03 - middleware - INFO - middleware - dispatch - 40 - [f3fa7c25-3a6e-491d-8d2f-be751c144350] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u60c5\u666f\u63cf\u5199\u7c7b", "disease_name": "\u9aa8\u8d28\u758f\u677e", "shop_name": "\u60a6\u548c\u56ed"}}
2025-06-05 09:44:03 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '情景描写类', 'disease_name': '骨质疏松', 'shop_name': '悦和园'}
2025-06-05 09:44:03 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:44:03 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '情景描写类', 'disease_name': '骨质疏松', 'shop_name': '悦和园'}
2025-06-05 09:44:03 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:44:04 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:44:04 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:44:19 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:44:19 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512275098586693684&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:44:19 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:44:19 - middleware - INFO - middleware - dispatch - 54 - [f3fa7c25-3a6e-491d-8d2f-be751c144350] Response: 200 - Time: 15.402s
2025-06-05 09:44:19 - middleware - INFO - middleware - dispatch - 29 - [a8f8bd5b-55e9-4ce8-9fe9-8b9c5a222ab4] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-05 09:44:19 - middleware - INFO - middleware - dispatch - 40 - [a8f8bd5b-55e9-4ce8-9fe9-8b9c5a222ab4] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u95f2\u804a\u7c7b", "disease_name": "\u9aa8\u8d28\u758f\u677e", "shop_name": "\u5174\u6d77\u56ed"}}
2025-06-05 09:44:19 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '闲聊类', 'disease_name': '骨质疏松', 'shop_name': '兴海园'}
2025-06-05 09:44:19 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:44:19 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '闲聊类', 'disease_name': '骨质疏松', 'shop_name': '兴海园'}
2025-06-05 09:44:19 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:44:20 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:44:20 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:44:34 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:44:34 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512275169222770738&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:44:34 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:44:34 - middleware - INFO - middleware - dispatch - 54 - [a8f8bd5b-55e9-4ce8-9fe9-8b9c5a222ab4] Response: 200 - Time: 14.446s
2025-06-05 09:44:34 - middleware - INFO - middleware - dispatch - 29 - [aec88bd6-f828-492a-bcc2-5b7d1cf79ec0] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-05 09:44:34 - middleware - INFO - middleware - dispatch - 40 - [aec88bd6-f828-492a-bcc2-5b7d1cf79ec0] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u65f6\u95f4\u5207\u5165\u7c7b", "disease_name": "\u5931\u7720", "shop_name": "\u5b89\u5b81\u534e\u5ead"}}
2025-06-05 09:44:34 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '时间切入类', 'disease_name': '失眠', 'shop_name': '安宁华庭'}
2025-06-05 09:44:34 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:44:34 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '时间切入类', 'disease_name': '失眠', 'shop_name': '安宁华庭'}
2025-06-05 09:44:34 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:44:35 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:44:35 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:44:46 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:44:46 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512275232028459048&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:44:46 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:44:46 - middleware - INFO - middleware - dispatch - 54 - [aec88bd6-f828-492a-bcc2-5b7d1cf79ec0] Response: 200 - Time: 11.957s
2025-06-05 09:44:46 - middleware - INFO - middleware - dispatch - 29 - [f4b2d8fb-bdbd-4681-ba30-f76fb3a284bd] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-05 09:44:46 - middleware - INFO - middleware - dispatch - 40 - [f4b2d8fb-bdbd-4681-ba30-f76fb3a284bd] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u60c5\u666f\u63cf\u5199\u7c7b", "disease_name": "\u9ad8\u8840\u538b", "shop_name": "\u5b89\u5b81\u534e\u5ead"}}
2025-06-05 09:44:46 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '情景描写类', 'disease_name': '高血压', 'shop_name': '安宁华庭'}
2025-06-05 09:44:46 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:44:46 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '情景描写类', 'disease_name': '高血压', 'shop_name': '安宁华庭'}
2025-06-05 09:44:46 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:44:48 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:44:48 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:45:03 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:45:03 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512275281253646376&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:45:03 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:45:03 - middleware - INFO - middleware - dispatch - 54 - [f4b2d8fb-bdbd-4681-ba30-f76fb3a284bd] Response: 200 - Time: 16.402s
2025-06-05 09:45:03 - middleware - INFO - middleware - dispatch - 29 - [d8c8e996-1a26-4420-a6c3-27905a169ab3] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-05 09:45:03 - middleware - INFO - middleware - dispatch - 40 - [d8c8e996-1a26-4420-a6c3-27905a169ab3] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u5185\u5fc3\u72ec\u767d\u7c7b", "disease_name": "\u9ad8\u8840\u538b", "shop_name": "\u7428\u5ead"}}
2025-06-05 09:45:03 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '内心独白类', 'disease_name': '高血压', 'shop_name': '琨庭'}
2025-06-05 09:45:03 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:45:03 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '内心独白类', 'disease_name': '高血压', 'shop_name': '琨庭'}
2025-06-05 09:45:03 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:45:05 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:45:05 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:45:21 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:45:21 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512275361137328140&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:45:21 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:45:21 - middleware - INFO - middleware - dispatch - 54 - [d8c8e996-1a26-4420-a6c3-27905a169ab3] Response: 200 - Time: 17.591s
2025-06-05 09:45:21 - middleware - INFO - middleware - dispatch - 29 - [0ee997dc-657e-4ac1-b5db-c24e1ef76f96] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: python-requests/2.32.3
2025-06-05 09:45:21 - middleware - INFO - middleware - dispatch - 40 - [0ee997dc-657e-4ac1-b5db-c24e1ef76f96] Request body: {"workflow_id": "7510526092345376831", "params": {"content_start": "\u60c5\u7eea\u7c7b", "disease_name": "\u5931\u7720", "shop_name": "\u6587\u5316\u8425"}}
2025-06-05 09:45:21 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '情绪类', 'disease_name': '失眠', 'shop_name': '文化营'}
2025-06-05 09:45:21 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:45:21 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '情绪类', 'disease_name': '失眠', 'shop_name': '文化营'}
2025-06-05 09:45:21 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:45:23 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:45:23 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:45:33 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:45:33 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512275438413742106&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:45:33 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:45:33 - middleware - INFO - middleware - dispatch - 54 - [0ee997dc-657e-4ac1-b5db-c24e1ef76f96] Response: 200 - Time: 11.884s
2025-06-05 09:52:21 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-05 09:52:21 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-05 09:52:21 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-05 09:52:21 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [67184]
2025-06-05 09:52:21 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-05 09:52:21 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-05 09:52:21 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-05 09:52:21 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [74816]
2025-06-05 09:52:22 - uvicorn.error - INFO - server - _serve - 83 - Started server process [72140]
2025-06-05 09:52:22 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-05 09:52:22 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-05 09:52:22 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-05 09:52:23 - uvicorn.error - INFO - server - _serve - 83 - Started server process [56628]
2025-06-05 09:52:23 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-05 09:52:23 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-05 09:52:23 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-05 09:53:10 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-05 09:53:10 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-05 09:53:10 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-05 09:53:10 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [72140]
2025-06-05 09:53:14 - uvicorn.error - INFO - server - _serve - 83 - Started server process [75112]
2025-06-05 09:53:14 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-05 09:53:14 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-05 09:53:14 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-05 09:53:24 - middleware - INFO - middleware - dispatch - 29 - [223397f3-77b6-4f97-8445-e11b58feba51] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-06-05 09:53:24 - middleware - INFO - middleware - dispatch - 40 - [223397f3-77b6-4f97-8445-e11b58feba51] Request body: {

    "workflow_id": "7510526092345376831",

    "params": {

        "content_start": "闲聊类",

        "disease_name": "高血压",

        "shop_name": "悦和园"

    }

}
2025-06-05 09:53:24 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '闲聊类', 'disease_name': '高血压', 'shop_name': '悦和园'}
2025-06-05 09:53:24 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:53:24 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '闲聊类', 'disease_name': '高血压', 'shop_name': '悦和园'}
2025-06-05 09:53:24 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:53:25 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:53:25 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:53:34 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:53:34 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512277509683314698&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:53:34 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:53:34 - middleware - INFO - middleware - dispatch - 54 - [223397f3-77b6-4f97-8445-e11b58feba51] Response: 200 - Time: 10.646s
2025-06-05 09:57:38 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-05 09:57:38 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-05 09:57:38 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-05 09:57:38 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [75112]
2025-06-05 09:57:40 - uvicorn.error - INFO - server - _serve - 83 - Started server process [77404]
2025-06-05 09:57:40 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-05 09:57:40 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-05 09:57:40 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-05 09:57:52 - middleware - INFO - middleware - dispatch - 29 - [2b2d5d3d-3c07-4d12-af51-870fd542cec7] POST http://127.0.0.1:8000/run-workflow - Client: 127.0.0.1 - User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-06-05 09:57:52 - middleware - INFO - middleware - dispatch - 40 - [2b2d5d3d-3c07-4d12-af51-870fd542cec7] Request body: {

    "workflow_id": "7510526092345376831",

    "params": {

        "content_start": "闲聊类",

        "disease_name": "高血压",

        "shop_name": "悦和园"

    }

}
2025-06-05 09:57:52 - main - INFO - main - execute_workflow - 41 - Received workflow request: workflow_id='7510526092345376831' params={'content_start': '闲聊类', 'disease_name': '高血压', 'shop_name': '悦和园'}
2025-06-05 09:57:52 - main - INFO - main - execute_workflow - 44 - Starting workflow execution for ID: 7510526092345376831
2025-06-05 09:57:52 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 32 - Executing workflow 7510526092345376831 with params: {'content_start': '闲聊类', 'disease_name': '高血压', 'shop_name': '悦和园'}
2025-06-05 09:57:52 - token_manager - INFO - token_manager - _refresh_token - 50 - 正在刷新 token...
2025-06-05 09:57:53 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-05 09:57:53 - token_manager - INFO - token_manager - _refresh_token - 55 - Token 刷新成功
2025-06-05 09:58:01 - httpx - INFO - _client - _send_single_request - 1038 - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-05 09:58:01 - coze_workflow - INFO - coze_workflow_pyq_media - execute - 38 - Workflow run created: debug_url='https://www.coze.cn/work_flow?execute_id=7512278651905163300&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2' data='' execute_id=None
2025-06-05 09:58:01 - main - INFO - main - execute_workflow - 51 - Workflow execution completed successfully
2025-06-05 09:58:01 - middleware - INFO - middleware - dispatch - 54 - [2b2d5d3d-3c07-4d12-af51-870fd542cec7] Response: 200 - Time: 9.290s
2025-06-05 09:58:36 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-05 09:58:36 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-05 09:58:36 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-05 09:58:36 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [77404]
2025-06-05 09:58:38 - uvicorn.error - INFO - server - _serve - 83 - Started server process [64536]
2025-06-05 09:58:38 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-05 09:58:38 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-05 09:58:38 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-05 09:59:20 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-05 09:59:20 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-05 09:59:20 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-05 09:59:20 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [64536]
2025-06-05 09:59:58 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-05 09:59:59 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-05 09:59:59 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-05 09:59:59 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [56628]
2025-06-05 09:59:59 - uvicorn.error - INFO - server - _serve - 83 - Started server process [49984]
2025-06-05 09:59:59 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-05 09:59:59 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-05 09:59:59 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-05 10:01:08 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-05 10:01:08 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-05 10:01:08 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-05 10:01:08 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [49984]
2025-06-05 10:01:09 - uvicorn.error - INFO - server - _serve - 83 - Started server process [80880]
2025-06-05 10:01:09 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-06-05 10:01:09 - main - INFO - main - startup_event - 19 - FastAPI application started - logging configured
2025-06-05 10:01:09 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-06-05 10:04:44 - uvicorn.error - INFO - server - shutdown - 263 - Shutting down
2025-06-05 10:04:44 - uvicorn.error - INFO - on - shutdown - 67 - Waiting for application shutdown.
2025-06-05 10:04:44 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-05 10:04:44 - uvicorn.error - INFO - server - _serve - 93 - Finished server process [80880]
