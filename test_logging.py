#!/usr/bin/env python3
"""
测试日志配置是否正常工作
"""
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_logging():
    """测试各种日志级别"""
    logger.debug("这是一个 DEBUG 消息")
    logger.info("这是一个 INFO 消息")
    logger.warning("这是一个 WARNING 消息")
    logger.error("这是一个 ERROR 消息")
    
    print("直接 print 输出")
    
    # 测试导入模块的日志
    try:
        from coze_workflow_pyq_media import WorkflowExecutor
        logger.info("成功导入 WorkflowExecutor")
        
        from token_manager import TokenManager
        logger.info("成功导入 TokenManager")
        
    except Exception as e:
        logger.error(f"导入模块时出错: {e}")

if __name__ == "__main__":
    logger.info("开始测试日志配置...")
    test_logging()
    logger.info("日志测试完成")
