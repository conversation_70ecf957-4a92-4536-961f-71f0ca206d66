2025-06-04 22:28:18,870 - __main__ - INFO - Starting debug server
2025-06-04 22:29:03,725 - __main__ - INFO - Root endpoint called
2025-06-04 22:29:24,590 - __main__ - INFO - Received workflow request: workflow_id='test123' params={'test': 'value'}
2025-06-04 22:29:25,640 - coze_workflow_pyq_media - INFO - Executing workflow test123 with params: {'test': 'value'}
2025-06-04 22:29:25,641 - token_manager - INFO - 正在刷新 token...
2025-06-04 22:29:27,821 - httpx - INFO - HTTP Request: POST https://api.coze.cn/api/permission/oauth2/token "HTTP/1.1 200 OK"
2025-06-04 22:29:27,823 - token_manager - INFO - Token 刷新成功
2025-06-04 22:29:29,024 - httpx - INFO - HTTP Request: POST https://api.coze.cn/v1/workflow/run "HTTP/1.1 200 OK"
2025-06-04 22:29:29,054 - coze_workflow_pyq_media - ERROR - 工作流执行失败: code: 4000, msg: The parameter workflow_id  is invalid. It should follow the format: test123. Please review your input., logid: 20250604222930BAF86B4E473D99053497
2025-06-04 22:29:29,057 - __main__ - ERROR - Workflow error: 工作流执行失败: code: 4000, msg: The parameter workflow_id  is invalid. It should follow the format: test123. Please review your input., logid: 20250604222930BAF86B4E473D99053497
