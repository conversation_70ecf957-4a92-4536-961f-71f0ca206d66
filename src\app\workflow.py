#!/usr/bin/env python3
"""
工作流执行模块
"""
from typing import Dict, Any
import urllib3
from dotenv import load_dotenv
from ..config.token_manager import TokenManager
from ..config.logger import get_logger

# 获取logger实例
logger = get_logger("workflow")

# 禁用SSL警告
urllib3.disable_warnings()

# 加载环境变量
load_dotenv()

class WorkflowExecutor:
    """工作流执行器"""
    
    def __init__(self):
        self.token_manager = TokenManager()

    def execute(self, workflow_id: str, params: dict) -> Dict[str, Any]:
        """
        执行工作流核心方法
        
        参数:
        workflow_id: 工作流ID
        params: 工作流参数
        
        返回:
        执行结果字典
        """
        try:
            logger.info(f"Executing workflow {workflow_id} with params: {params}")
            coze_client = self.token_manager.get_coze_client()
            result = coze_client.workflows.runs.create(
                workflow_id=workflow_id,
                parameters={"params": params}
            )
            logger.info(f"Workflow run created: {result}")
            return result
        except Exception as e:
            logger.error(f"工作流执行失败: {str(e)}")
            raise RuntimeError(f"工作流执行失败: {str(e)}")

# 保留原始函数兼容旧调用
def run_coze_workflow(workflow_id: str, params: dict) -> Dict:
    """
    运行Coze工作流的便捷函数
    
    Args:
        workflow_id: 工作流ID
        params: 工作流参数
    
    Returns:
        工作流执行结果
    """
    executor = WorkflowExecutor()
    return executor.execute(workflow_id, params)
