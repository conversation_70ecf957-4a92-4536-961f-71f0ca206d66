#!/usr/bin/env python3
"""
API客户端使用示例
"""
import requests
import json

class PyQAutoPublishClient:
    """PyQ Auto Publish API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip("/")
    
    def health_check(self):
        """健康检查"""
        response = requests.get(f"{self.base_url}/health")
        return response.json()
    
    def run_workflow(self, workflow_id: str, params: dict):
        """执行工作流"""
        data = {
            "workflow_id": workflow_id,
            "params": params
        }
        response = requests.post(
            f"{self.base_url}/run-workflow",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        return response.json()

def main():
    """主函数"""
    client = PyQAutoPublishClient()
    
    # 健康检查
    print("健康检查:")
    health = client.health_check()
    print(json.dumps(health, indent=2, ensure_ascii=False))
    
    # 执行工作流
    print("\n执行工作流:")
    try:
        result = client.run_workflow(
            workflow_id="your_workflow_id_here",
            params={"key": "value"}
        )
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"执行失败: {e}")

if __name__ == "__main__":
    main()
