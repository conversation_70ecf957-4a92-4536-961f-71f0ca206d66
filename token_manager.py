import time
import os
import logging
import sys
from dotenv import load_dotenv
from typing import Optional
from cozepy import Coze, TokenAuth, JWTOAuthApp, COZE_CN_BASE_URL

# Configure detailed logging if not already configured
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

# 获取logger实例
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

class TokenManager:
    def __init__(self):
        self.client_id = os.getenv("COZE_JWT_OAUTH_CLIENT_ID")
        private_key_path = os.getenv("COZE_JWT_OAUTH_PRIVATE_KEY_PATH")
        if not private_key_path:
            raise ValueError("COZE_JWT_OAUTH_PRIVATE_KEY_PATH 环境变量未配置")
        if not os.path.exists(private_key_path):
            raise FileNotFoundError(f"私钥文件不存在: {private_key_path}")
        
        with open(private_key_path, "r") as f:
            self.private_key = f.read()
        self.public_key_id = os.getenv("COZE_JWT_OAUTH_PUBLIC_KEY_ID")
        self._access_token = None
        self._token_expires_at = 0
        
        
        self.jwt_oauth_app = JWTOAuthApp(
            client_id=self.client_id,
            private_key=self.private_key,
            public_key_id=self.public_key_id,
            base_url=COZE_CN_BASE_URL
        )
        
    def get_token(self) -> str:
        """获取有效的 token，如果过期则自动刷新"""
        current_time = time.time()
        
        # 如果 token 不存在或即将过期（预留 60s 缓冲），则刷新
        if not self._access_token or current_time >= (self._token_expires_at - 60):
            self._refresh_token()
            
        return self._access_token
    
    def _refresh_token(self):
        """刷新 token"""
        try:
            logger.info("正在刷新 token...")
            oauth_token = self.jwt_oauth_app.get_access_token(ttl=3600)
            self._access_token = oauth_token.access_token
            # 设置过期时间
            self._token_expires_at = time.time() + 3600
            logger.info("Token 刷新成功")
        except Exception as e:
            logger.error(f"刷新 token 失败: {str(e)}")
            raise
    
    def get_coze_client(self) -> Coze:
        """获取配置好的 Coze 客户端"""
        token = self.get_token()
        return Coze(
            auth=TokenAuth(token),
            base_url=COZE_CN_BASE_URL
        )
