[2025-06-04 22:26:50.985022] Test logging endpoint called
[2025-06-04 22:27:23.117286] API called with: workflow_id='test123' params={'test': 'value'}
[2025-06-04 22:27:23.122468] About to call workflow
[2025-06-04 22:27:25.311972] Error: 工作流执行失败: code: 4000, msg: The parameter workflow_id  is invalid. It should follow the format: test123. Please review your input., logid: 202506042227263F2EA2CECA584C4FC1D5
[2025-06-04 22:30:59.943759] Test logging endpoint called
[2025-06-04 22:31:16.104852] API called with: workflow_id='test123' params={'test': 'value'}
[2025-06-04 22:31:16.108411] About to call workflow
[2025-06-04 22:31:18.274098] Error: 工作流执行失败: code: 4000, msg: The parameter workflow_id  is invalid. It should follow the format: test123. Please review your input., logid: 202506042231191467ED12E8FBD4A24074
