#!/usr/bin/env python3
"""
FastAPI 主应用
"""
from fastapi import FastAPI, HTTPException
from datetime import datetime
from .models import WorkflowRequest, WorkflowResponse, HealthResponse
from .workflow import run_coze_workflow
from ..config.logger import setup_logging, get_logger
from ..config.middleware import LoggingMiddleware
from .. import __version__

# 设置日志
setup_logging()
logger = get_logger("main")

# 创建FastAPI应用
app = FastAPI(
    title="PyQ Auto Publish API",
    description="Coze Workflow API for PyQ Auto Publish",
    version=__version__,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加日志中间件
app.add_middleware(LoggingMiddleware)

@app.on_event("startup")
async def startup_event():
    """应用启动时的事件处理"""
    logger.info("FastAPI application started - logging configured")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的事件处理"""
    logger.info("FastAPI application shutdown")

@app.get("/", response_model=dict)
async def root():
    """根路径"""
    logger.info("Root endpoint accessed")
    return {
        "message": "PyQ Auto Publish API",
        "version": __version__,
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    logger.info("Health check endpoint accessed")
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version=__version__
    )

@app.get("/test-logging")
async def test_logging():
    """测试日志功能的简单端点"""
    logger.info("Test logging endpoint was called")
    logger.debug("This is a debug message")
    logger.warning("This is a warning message")
    
    return {
        "message": "Test logging successful", 
        "timestamp": datetime.now().isoformat(),
        "note": "Check logs/app.log for detailed logs"
    }

@app.post("/run-workflow", response_model=WorkflowResponse)
async def execute_workflow(request: WorkflowRequest):
    """执行工作流"""
    logger.info(f"Received workflow request: {request}")
    
    try:
        logger.info(f"Starting workflow execution for ID: {request.workflow_id}")
        
        result = run_coze_workflow(
            workflow_id=request.workflow_id,
            params=request.params
        )
        
        logger.info(f"Workflow execution completed successfully")
        logger.debug(f"Workflow result: {result}")
        
        return WorkflowResponse(
            status="success",
            data=result,
            message="Workflow executed successfully"
        )
        
    except Exception as e:
        logger.error(f"Workflow execution failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
