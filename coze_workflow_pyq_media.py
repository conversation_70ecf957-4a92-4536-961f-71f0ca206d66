from typing import Dict, Any
from token_manager import TokenManager
from dotenv import load_dotenv
import os
import urllib3
import logging
import sys

# Configure detailed logging if not already configured
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

# 获取logger实例
logger = logging.getLogger(__name__)

# 禁用SSL警告
urllib3.disable_warnings()

load_dotenv()

class WorkflowExecutor:
    def __init__(self):
        self.token_manager = TokenManager()

    def execute(self, workflow_id: str, params: dict) -> Dict[str, Any]:
        """
        执行工作流核心方法
        
        参数:
        workflow_id: 工作流ID
        params: 工作流参数
        
        返回:
        执行结果字典
        """
        try:
            logger.info(f"Executing workflow {workflow_id} with params: {params}")
            coze_client = self.token_manager.get_coze_client()
            result = coze_client.workflows.runs.create(
                workflow_id=workflow_id,
                parameters={"params": params}
            )
            logger.info(f"Workflow run created: {result}")
            return result
        except Exception as e:
            logger.error(f"工作流执行失败: {str(e)}")
            raise RuntimeError(f"工作流执行失败: {str(e)}")

# 保留原始函数兼容旧调用
def run_coze_workflow(workflow_id: str, params: dict) -> Dict:
    executor = WorkflowExecutor()
    return executor.execute(workflow_id, params)
