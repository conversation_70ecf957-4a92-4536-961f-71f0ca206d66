#!/usr/bin/env python3
"""
服务器启动脚本
"""
import uvicorn
from src.app.main import app
from src.config.logger import get_logger

# 获取logger
logger = get_logger("server")

def main():
    """启动服务器"""
    logger.info("Starting PyQ Auto Publish API server...")
    
    uvicorn.run(
        "src.app.main:app",
        host="0.0.0.0",
        port=8001,
        log_level="info",
        access_log=True,
        reload=False  # 生产环境建议设为False
    )

if __name__ == "__main__":
    main()
