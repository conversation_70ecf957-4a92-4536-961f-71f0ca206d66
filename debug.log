[DEBUG] Debug main.py starting...
[DEBUG] Starting debug server...
[DEBUG] Root endpoint called
[DEBUG] Workflow endpoint called with: workflow_id='test123' params={'test': 'value'}
[DEBUG] About to import and call workflow function
[DEBUG] Successfully imported workflow function
[DEBUG] Error in workflow: 工作流执行失败: code: 4000, msg: The parameter workflow_id  is invalid. It should follow the format: test123. Please review your input., logid: 20250604222930BAF86B4E473D99053497
