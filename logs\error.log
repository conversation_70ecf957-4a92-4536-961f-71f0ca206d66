2025-06-04 22:43:58 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042243595B34E3DD38133A0B3823, debug_url: https://www.coze.cn/work_flow?execute_id=7512104974790098978&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:43:58 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042243595B34E3DD38133A0B3823, debug_url: https://www.coze.cn/work_flow?execute_id=7512104974790098978&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042243595B34E3DD38133A0B3823, debug_url: https://www.coze.cn/work_flow?execute_id=7512104974790098978&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042243595B34E3DD38133A0B3823, debug_url: https://www.coze.cn/work_flow?execute_id=7512104974790098978&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:00 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244022FB34532B52E821CB956, debug_url: https://www.coze.cn/work_flow?execute_id=7512104986295058451&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:00 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244022FB34532B52E821CB956, debug_url: https://www.coze.cn/work_flow?execute_id=7512104986295058451&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244022FB34532B52E821CB956, debug_url: https://www.coze.cn/work_flow?execute_id=7512104986295058451&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244022FB34532B52E821CB956, debug_url: https://www.coze.cn/work_flow?execute_id=7512104986295058451&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:03 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244046DD6003D4890BC0F2731, debug_url: https://www.coze.cn/work_flow?execute_id=7512105003243274274&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:03 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244046DD6003D4890BC0F2731, debug_url: https://www.coze.cn/work_flow?execute_id=7512105003243274274&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244046DD6003D4890BC0F2731, debug_url: https://www.coze.cn/work_flow?execute_id=7512105003243274274&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244046DD6003D4890BC0F2731, debug_url: https://www.coze.cn/work_flow?execute_id=7512105003243274274&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:06 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422440734C90240D3BE43310453, debug_url: https://www.coze.cn/work_flow?execute_id=7512105013724921919&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:06 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422440734C90240D3BE43310453, debug_url: https://www.coze.cn/work_flow?execute_id=7512105013724921919&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422440734C90240D3BE43310453, debug_url: https://www.coze.cn/work_flow?execute_id=7512105013724921919&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422440734C90240D3BE43310453, debug_url: https://www.coze.cn/work_flow?execute_id=7512105013724921919&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:09 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224410B9AA2BAF026A1DAA89D0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105030649643046&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:09 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224410B9AA2BAF026A1DAA89D0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105030649643046&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224410B9AA2BAF026A1DAA89D0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105030649643046&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224410B9AA2BAF026A1DAA89D0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105030649643046&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:12 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224413BC2A47401EBA8BE0A20A, debug_url: https://www.coze.cn/work_flow?execute_id=7512105031010779151&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:12 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224413BC2A47401EBA8BE0A20A, debug_url: https://www.coze.cn/work_flow?execute_id=7512105031010779151&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224413BC2A47401EBA8BE0A20A, debug_url: https://www.coze.cn/work_flow?execute_id=7512105031010779151&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224413BC2A47401EBA8BE0A20A, debug_url: https://www.coze.cn/work_flow?execute_id=7512105031010779151&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:15 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244160E303E245B5CFC0C9273, debug_url: https://www.coze.cn/work_flow?execute_id=7512105044574896180&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:15 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244160E303E245B5CFC0C9273, debug_url: https://www.coze.cn/work_flow?execute_id=7512105044574896180&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244160E303E245B5CFC0C9273, debug_url: https://www.coze.cn/work_flow?execute_id=7512105044574896180&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042244160E303E245B5CFC0C9273, debug_url: https://www.coze.cn/work_flow?execute_id=7512105044574896180&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:17 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224419D972CBFCE808FF255DD2, debug_url: https://www.coze.cn/work_flow?execute_id=7512105065093513231&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:17 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224419D972CBFCE808FF255DD2, debug_url: https://www.coze.cn/work_flow?execute_id=7512105065093513231&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224419D972CBFCE808FF255DD2, debug_url: https://www.coze.cn/work_flow?execute_id=7512105065093513231&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224419D972CBFCE808FF255DD2, debug_url: https://www.coze.cn/work_flow?execute_id=7512105065093513231&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:20 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224421DA0B1A1BC85EA6FE8CF6, debug_url: https://www.coze.cn/work_flow?execute_id=7512105074710741027&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:20 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224421DA0B1A1BC85EA6FE8CF6, debug_url: https://www.coze.cn/work_flow?execute_id=7512105074710741027&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224421DA0B1A1BC85EA6FE8CF6, debug_url: https://www.coze.cn/work_flow?execute_id=7512105074710741027&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224421DA0B1A1BC85EA6FE8CF6, debug_url: https://www.coze.cn/work_flow?execute_id=7512105074710741027&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:23 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224424B97DF35E11762139E13F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105082797522998&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:44:23 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224424B97DF35E11762139E13F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105082797522998&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224424B97DF35E11762139E13F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105082797522998&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224424B97DF35E11762139E13F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105082797522998&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:14 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224615937C2D792E470A5F4CB8, debug_url: https://www.coze.cn/work_flow?execute_id=7512105554011013183&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:14 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224615937C2D792E470A5F4CB8, debug_url: https://www.coze.cn/work_flow?execute_id=7512105554011013183&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224615937C2D792E470A5F4CB8, debug_url: https://www.coze.cn/work_flow?execute_id=7512105554011013183&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224615937C2D792E470A5F4CB8, debug_url: https://www.coze.cn/work_flow?execute_id=7512105554011013183&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:17 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422461893DE5B6CCD9B448DEC26, debug_url: https://www.coze.cn/work_flow?execute_id=7512105573866012735&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:17 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422461893DE5B6CCD9B448DEC26, debug_url: https://www.coze.cn/work_flow?execute_id=7512105573866012735&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422461893DE5B6CCD9B448DEC26, debug_url: https://www.coze.cn/work_flow?execute_id=7512105573866012735&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422461893DE5B6CCD9B448DEC26, debug_url: https://www.coze.cn/work_flow?execute_id=7512105573866012735&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:19 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224620732E6C95D1BF941DF9E0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105582658355235&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:19 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224620732E6C95D1BF941DF9E0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105582658355235&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224620732E6C95D1BF941DF9E0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105582658355235&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224620732E6C95D1BF941DF9E0, debug_url: https://www.coze.cn/work_flow?execute_id=7512105582658355235&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:22 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042246232FE347D8D998242DC0BF, debug_url: https://www.coze.cn/work_flow?execute_id=7512105601443708962&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:46:22 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042246232FE347D8D998242DC0BF, debug_url: https://www.coze.cn/work_flow?execute_id=7512105601443708962&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042246232FE347D8D998242DC0BF, debug_url: https://www.coze.cn/work_flow?execute_id=7512105601443708962&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 202506042246232FE347D8D998242DC0BF, debug_url: https://www.coze.cn/work_flow?execute_id=7512105601443708962&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:53 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224754D9F146E33D6E64B29E6F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105996391907379&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:53 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224754D9F146E33D6E64B29E6F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105996391907379&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224754D9F146E33D6E64B29E6F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105996391907379&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224754D9F146E33D6E64B29E6F, debug_url: https://www.coze.cn/work_flow?execute_id=7512105996391907379&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:56 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422475769108196F78225AACFE1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106001509367834&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:56 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422475769108196F78225AACFE1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106001509367834&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422475769108196F78225AACFE1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106001509367834&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 2025060422475769108196F78225AACFE1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106001509367834&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:59 - coze_workflow - ERROR - coze_workflow_pyq_media - execute - 41 - 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224800F05A36FFA6DA3298BDA1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106020496523302&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
2025-06-04 22:47:59 - main - ERROR - main - execute_workflow - 57 - Workflow execution failed: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224800F05A36FFA6DA3298BDA1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106020496523302&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 34, in execute
    result = coze_client.workflows.runs.create(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\workflows\runs\__init__.py", line 195, in create
    return self._requester.request("post", url, False, WorkflowRunResult, body=remove_none_values(body))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 273, in request
    return self.send(request)
           ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 282, in send
    return self._parse_response(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\AutoBox\boxes\coze_api_key\.venv\Lib\site-packages\cozepy\request.py", line 482, in _parse_response
    raise CozeAPIError(code, msg, logid, debug_url)
cozepy.exception.CozeAPIError: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224800F05A36FFA6DA3298BDA1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106020496523302&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wample\coding\me\pyq_auto_publish\main.py", line 46, in execute_workflow
    result = run_coze_workflow(
             ^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 47, in run_coze_workflow
    return executor.execute(workflow_id, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\wample\coding\me\pyq_auto_publish\coze_workflow_pyq_media.py", line 42, in execute
    raise RuntimeError(f"工作流执行失败: {str(e)}")
RuntimeError: 工作流执行失败: code: 4000, msg: Missing required parameters. Please review the API documentation and ensure all mandatory fields are included in your request., logid: 20250604224800F05A36FFA6DA3298BDA1, debug_url: https://www.coze.cn/work_flow?execute_id=7512106020496523302&space_id=7424443666821316649&workflow_id=7510526092345376831&execute_mode=2
