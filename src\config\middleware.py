#!/usr/bin/env python3
"""
FastAPI 中间件
"""
import time
import uuid
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from logger_config import get_logger

logger = get_logger("middleware")

class LoggingMiddleware(BaseHTTPMiddleware):
    """
    日志记录中间件
    """
    
    async def dispatch(self, request: Request, call_next):
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 记录请求开始
        start_time = time.time()
        
        # 获取客户端IP
        client_ip = request.client.host if request.client else "unknown"
        
        # 记录请求信息
        logger.info(
            f"[{request_id}] {request.method} {request.url} - "
            f"Client: {client_ip} - User-Agent: {request.headers.get('user-agent', 'unknown')}"
        )
        
        # 如果是POST请求，尝试记录请求体（小心处理大文件）
        if request.method == "POST":
            try:
                # 读取请求体
                body = await request.body()
                if len(body) < 1024:  # 只记录小于1KB的请求体
                    logger.info(f"[{request_id}] Request body: {body.decode('utf-8', errors='ignore')}")
                else:
                    logger.info(f"[{request_id}] Request body too large ({len(body)} bytes)")
            except Exception as e:
                logger.warning(f"[{request_id}] Could not read request body: {e}")
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            logger.info(
                f"[{request_id}] Response: {response.status_code} - "
                f"Time: {process_time:.3f}s"
            )
            
            # 添加请求ID到响应头
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # 记录异常
            process_time = time.time() - start_time
            logger.error(
                f"[{request_id}] Error processing request: {str(e)} - "
                f"Time: {process_time:.3f}s"
            )
            raise
