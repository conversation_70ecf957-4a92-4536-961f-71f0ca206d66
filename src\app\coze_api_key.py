
import os
import urllib3
from token_manager import TokenManager

# 禁用 SSL 警告
urllib3.disable_warnings()


# 获取配置
try:
    # 创建 token 管理器（自动从环境变量获取配置）
    token_manager = TokenManager()
    
    # 获取 Coze 客户端
    coze = token_manager.get_coze_client()
    
    # 运行工作流
    workflow_id = "7503585906826215460"
    
    workflow_result = coze.workflows.runs.create(
        workflow_id=workflow_id,
    )
    print('workflow_result:',workflow_result)
    
except Exception as e:
    print(f"执行过程中发生错误: {str(e)}")
    raise
